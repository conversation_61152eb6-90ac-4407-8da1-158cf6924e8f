
#include "my_graphics.h" // Just include our own header!
#include <cmath>

int main() {
    // Initialize our graphics window
    graphics::initWindow(800, 600, "My Custom Graphics Engine!");

    float circleX = 400;
    float angle = 0;

    // The main game loop
    while (graphics::isWindowOpen()) {
        // --- Logic ---
        // Animate the circle's position
        angle += 0.01f;
        circleX = 400 + cos(angle) * 100;

        // --- Drawing ---
        graphics::clearScreen();
        graphics::drawCircle(circleX, 300, 50, graphics::Color::Blue);
        graphics::drawCircle(150, 200, 80, graphics::Color::Red);
        graphics::display<PERSON>rame();
    }
    
    return 0;
}
