#ifndef MY_GRAPHICS_H
#define MY_GRAPHICS_H

#include <string>

// Use a namespace to avoid name conflicts
namespace graphics {

    // A simple color enum to make color selection easy
    enum class Color {
        Red,
        Green,
        Blue,
        Black,
        White
    };

    // Initializes and creates the game window
    void initWindow(int width, int height, const std::string& title);

    // Checks if the window is still open and processes basic events
    bool isWindowOpen();

    // Clears the screen with a default color
    void clearScreen();

    // Draws a circle on the screen
    void drawCircle(int x, int y, int radius, Color color);

    // Displays everything that has been drawn in the current frame
    void displayFrame();

} // namespace graphics

#endif // MY_GRAPHICS_H