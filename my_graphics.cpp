
#include "my_graphics.h"
#include <iostream>
#include <thread>
#include <chrono>

// Simple console-based implementation for demonstration
namespace {
    bool windowOpen = false;
    int frameCount = 0;
}

namespace graphics {

    // Converts our simple Color enum to a string representation
    std::string colorToString(Color color) {
        switch (color) {
            case Color::Red:   return "Red";
            case Color::Green: return "Green";
            case Color::Blue:  return "Blue";
            case Color::White: return "White";
            case Color::Black: return "Black";
            default:           return "Unknown";
        }
    }

    void initWindow(int width, int height, const std::string& title) {
        std::cout << "Initializing window: " << title << " (" << width << "x" << height << ")" << std::endl;
        windowOpen = true;
        frameCount = 0;
    }

    bool isWindowOpen() {
        // Simple simulation - run for about 100 frames then close
        return windowOpen && frameCount < 100;
    }

    void clearScreen() {
        if (windowOpen) {
            std::cout << "\n--- Frame " << frameCount << " ---" << std::endl;
        }
    }

    void drawCircle(int x, int y, int radius, Color color) {
        if (windowOpen) {
            std::cout << "Drawing " << colorToString(color) << " circle at ("
                      << x << ", " << y << ") with radius " << radius << std::endl;
        }
    }

    void displayFrame() {
        if (windowOpen) {
            frameCount++;
            // Add a small delay to simulate frame rate
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }

} // namespace graphics
