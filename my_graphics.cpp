#include "my_graphics.h"
#include <SFML/Graphics.hpp>

// Hide SFML details within this .cpp file
namespace {
    // A pointer to the main window, managed internally
    sf::RenderWindow* window = nullptr;
}

namespace graphics {

    // Converts our simple Color enum to SFML's Color object
    sf::Color toSfmlColor(Color color) {
        switch (color) {
            case Color::Red:   return sf::Color::Red;
            case Color::Green: return sf::Color::Green;
            case Color::Blue:  return sf::Color::Blue;
            case Color::White: return sf::Color::White;
            case Color::Black: return sf::Color::Black;
            default:           return sf::Color::Black;
        }
    }

    void initWindow(int width, int height, const std::string& title) {
        // Create the window instance only if it doesn't exist
        if (!window) {
            window = new sf::RenderWindow(sf::VideoMode(width, height), title);
        }
    }

    bool isWindowOpen() {
        if (!window) return false;

        sf::Event event;
        while (window->pollEvent(event)) {
            if (event.type == sf::Event::Closed) {
                window->close();
            }
        }
        return window->isOpen();
    }

    void clearScreen() {
        if (window) {
            window->clear(sf::Color(200, 200, 200)); // A light grey
        }
    }

    void drawCircle(int x, int y, int radius, Color color) {
        if (window) {
            sf::CircleShape shape(radius);
            shape.setOrigin(radius, radius); // Set origin to the center
            shape.setPosition(x, y);
            shape.setFillColor(toSfmlColor(color));
            window->draw(shape);
        }
    }

    void displayFrame() {
        if (window) {
            window->display();
        }
    }

} // namespace graphics